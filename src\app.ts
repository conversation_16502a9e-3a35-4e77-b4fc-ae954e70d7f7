
// const express = require('express');
import express from 'express';
// const cors = require('cors');
import cors from 'cors';
import dotenv from 'dotenv';
dotenv.config();

// Import middleware
// const {
//   corsOptions,
//   limiter,
//   helmetConfig,
//   requestLogger,
//   validateInput,
//   compression
// } = require('./middleware/security');
import { corsOptions, limiter, helmetConfig, requestLogger, validateInput, compression } from './middleware/security';

// const { errorHandler, notFound } = require('./middleware/errorHandler');
import { errorHandler, notFound } from './middleware/errorHandler';

// Import database
// const { connectDB, disconnectDB } = require('./config/database');
import { connectDB, disconnectDB } from './config/database';

// Import routes
// const authRoutes = require('./routes/auth');
// const userRoutes = require('./routes/users');
import authRoutes from './routes/auth';
import userRoutes from './routes/users';

const app = express();

// Connect to database
connectDB();

// Security middleware
app.use(helmetConfig);
app.use(cors(corsOptions));
app.use(compression);
app.use(requestLogger);
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input validation middleware
app.use(validateInput);

// Health check endpoint
app.get('/health', (req: any, res: any) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// API routes
app.use(`${process.env.BASE_URL}/auth`, authRoutes);
app.use(`${process.env.BASE_URL}/users`, userRoutes);

// 404 handler
app.use('*', notFound);

// Global error handler
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    disconnectDB();
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    disconnectDB();
    process.exit(0);
  });
});

// module.exports = app;
export default app;
