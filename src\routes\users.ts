// const express = require('express');
import express from 'express';
const router = express.Router();

// const {
//   getAllUsers,
//   getUserById,
//   updateUserRole,
//   toggleUserStatus,
//   deleteUser
// } = require('../controllers/userController');
import {
  getAllUsers,
  getUserById,
  updateUserRole,
  toggleUserStatus,
  deleteUser
} from '../controllers/userController';

// const { authenticateToken, requireAdmin } = require('../middleware/auth');
// const { body } = require('express-validator');
// const { handleValidationErrors } = require('../middleware/validation');
import { authenticateToken, requireAdmin } from '../middleware/auth';
import { body } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation';

// All routes require admin access
router.use(authenticateToken, requireAdmin);

// Get all users
router.get('/', getAllUsers);

// Get user by ID
router.get('/:id', getUserById);

// Update user role
router.put('/:id/role', [
  body('role').isIn(['USER', 'ADMIN']).withMessage('Role must be USER or ADMIN'),
  handleValidationErrors
], updateUserRole);

// Toggle user active status
router.patch('/:id/toggle-status', toggleUserStatus);

// Delete user
router.delete('/:id', deleteUser);

// module.exports = router;
export default router;
