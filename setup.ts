#!/usr/bin/env node

// const fs = require('fs');
// const path = require('path');
// const { execSync } = require('child_process');
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🚀 Setting up BC Backend...\n');

// Check if .env exists
if (!fs.existsSync('.env')) {
  console.log('❌ .env file not found!');
  console.log('📝 Please create a .env file with the following variables:');
  console.log(`
DATABASE_URL="postgresql://username:password@localhost:5432/bc_database"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="24h"
PORT=5000
NODE_ENV=development
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN="http://localhost:3000"
  `);
  process.exit(1);
}

console.log('✅ .env file found');

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    console.log('❌ Failed to install dependencies');
    process.exit(1);
  }
} else {
  console.log('✅ Dependencies already installed');
}

// Generate Prisma client
console.log('🔧 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated');
} catch (error) {
  console.log('❌ Failed to generate Prisma client');
  process.exit(1);
}

// Check database connection and run migrations
console.log('🗃️  Setting up database...');
try {
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });
  console.log('✅ Database migrations completed');
} catch (error) {
  console.log('⚠️  Database migration failed. Please check your DATABASE_URL and ensure PostgreSQL is running.');
  console.log('You can run migrations manually later with: npm run db:migrate');
}

console.log('\n🎉 Setup completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Make sure PostgreSQL is running');
console.log('2. Update your .env file with correct database credentials');
console.log('3. Run: npm run dev');
console.log('4. Test the API at: http://localhost:5000/health');
console.log('\n📚 Check README.md for API documentation and usage examples.');
