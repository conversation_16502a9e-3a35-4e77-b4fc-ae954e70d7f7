// const helmet = require('helmet');
// const cors = require('cors');
import helmet from 'helmet';
import cors from 'cors';
// const rateLimit = require('express-rate-limit');
// const morgan = require('morgan');
import rateLimit from 'express-rate-limit';
import morgan from 'morgan';
import compression from 'compression';

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
};

// Rate limiting configuration
const limiter = rateLimit({
  // windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  // max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.'
  },
  skipSuccessfulRequests: true,
});

// Helmet configuration for security headers
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// Request logging
const requestLogger = morgan(
  process.env.NODE_ENV === 'development' 
    ? 'dev' 
    : 'combined'
);

// Input validation middleware
const validateInput = (req: any, res: any, next: any) => {
  // Remove any null bytes
  const sanitizeString = (str: any) => {
    if (typeof str === 'string') {
      return str.replace(/\0/g, '');
    }
    return str;
  };

  // Recursively sanitize object
  const sanitizeObject = (obj: any) => {
    if (obj && typeof obj === 'object') {
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          obj[key] = sanitizeObject(obj[key]);
        }
      }
    } else if (typeof obj === 'string') {
      return sanitizeString(obj);
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  if (req.params) {
    req.params = sanitizeObject(req.params);
  }

  next();
};

// module.exports = {
//   corsOptions,
//   limiter,
//   authLimiter,
//   helmetConfig,
//   requestLogger,
//   validateInput,
//   compression: compression()
// };

export {
  corsOptions,
  limiter,
  authLimiter,
  helmetConfig,
  requestLogger,
  validateInput,
  compression
};
