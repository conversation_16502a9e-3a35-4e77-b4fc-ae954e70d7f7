// Global error handler
const errorHandler = (err: any, req: any, res: any, next: any) => {
  console.error('Error:', err);

  // Default error
  let error = {
    success: false,
    message: err.message || 'Internal Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  };

  // Prisma errors
  if (err.code === 'P2002') {
    error.message = 'Duplicate field value entered';
    return res.status(400).json(error);
  }

  if (err.code === 'P2014') {
    error.message = 'Invalid ID';
    return res.status(400).json(error);
  }

  if (err.code === 'P2003') {
    error.message = 'Invalid input data';
    return res.status(400).json(error);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error.message = 'Invalid token';
    return res.status(401).json(error);
  }

  if (err.name === 'TokenExpiredError') {
    error.message = 'Token expired';
    return res.status(401).json(error);
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    error.message = 'Validation Error';
    return res.status(400).json(error);
  }

  // Default to 500 server error
  res.status(err.statusCode || 500).json(error);
};

// 404 handler
const notFound = (req: any, res: any) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
};

// module.exports = { errorHandler, notFound };
export { errorHandler, notFound };
