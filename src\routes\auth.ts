// const express = require('express');
import express from 'express';
const router = express.Router();

// const {
//   register,
//   login,
//   getProfile,
//   updateProfile,
//   changePassword
// } = require('../controllers/authController');
import {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword
} from '../controllers/authController';

// const { authenticateToken } = require('../middleware/auth');
// const { authLimiter } = require('../middleware/security');
// const {
//   validateRegister,
//   validateLogin,
//   validateProfileUpdate,
//   validatePasswordChange
// } = require('../middleware/validation');
import { authenticateToken } from '../middleware/auth';
import { authLimiter } from '../middleware/security';
import { validateRegister, validateLogin, validateProfileUpdate, validatePasswordChange } from '../middleware/validation';

// Public routes
router.post('/register', authLimiter, validateRegister, register);
router.post('/login', authLimiter, validateLogin, login);

// Protected routes
router.get('/profile', authenticateToken, getProfile);
router.put('/profile', authenticateToken, validateProfileUpdate, updateProfile);
router.put('/change-password', authenticateToken, validatePasswordChange, changePassword);

// module.exports = router;
export default router;
