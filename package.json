{"name": "bc-backend", "version": "1.0.0", "description": "Secure Node.ts backend with JWT authentication", "main": "src/app.ts", "scripts": {"start": "node src/app.ts", "dev": "nodemon src/app.ts", "setup": "node setup.ts", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio"}, "keywords": ["nodejs", "express", "jwt", "postgresql", "security"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "prisma": "^5.7.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}